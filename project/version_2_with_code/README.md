# Bad Chess - version 2, with code logic

This project contains the source code and data for the "Bad Chess" toy after doing both the corresponding article parts:

1. https://castle-engine.io/bad-chess/castle_game_engine_bad_chess_1.html

2. https://castle-engine.io/bad-chess/castle_game_engine_bad_chess_2.html

Have fun! If you run into any problems, don't hesitate to ping us [at our forum or Discord](https://castle-engine.io/talk.php).

Using [Castle Game Engine](https://castle-engine.io/).

## Building

Compile by:

- [CGE editor](https://castle-engine.io/editor). Just use menu items _"Compile"_ or _"Compile And Run"_.

- Or use [CGE command-line build tool](https://castle-engine.io/build_tool). Run `castle-engine compile` in this directory.

- Or use [Lazarus](https://www.lazarus-ide.org/). Open in Lazarus `bad_chess_project_standalone.lpi` file and compile / run from Lazarus. Make sure to first register [CGE Lazarus packages](https://castle-engine.io/lazarus).

- Or use [Delphi](https://www.embarcadero.com/products/Delphi). Open in Delphi `bad_chess_project_standalone.dproj` file and compile / run from Delphi. See [CGE and Delphi](https://castle-engine.io/delphi) documentation for details.