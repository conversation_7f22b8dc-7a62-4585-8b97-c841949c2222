{"$$ClassName": "TCastleUserInterface", "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "FullSize": true, "Name": "Group1", "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent"}, "$Children": [{"$$ClassName": "TCastleViewport", "Background": "Background1", "BackgroundColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "Camera": "Camera1", "FullSize": true, "Items": {"$$ClassName": "TCastleRootTransform", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "PhysicsProperties": {"$$ClassName": "TPhysicsProperties", "LayerCollisions": {"$$ClassName": "TCastleLayerCollisions"}, "LayerNames": {"$$ClassName": "TCastleLayerNames"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "$Children": [{"$$ClassName": "TCastleCamera", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "GravityUpPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "Camera1", "Orthographic": {"$$ClassName": "TCastleOrthographic", "OriginPersistent": {"$$ClassName": "TCastleVector2Persistent"}}, "Perspective": {"$$ClassName": "TCastlePerspective"}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 3.146969795227051, "X": -0.00013316853437572718, "Y": 0.9869927167892456, "Z": 0.1607653945684433}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent", "Y": 1.0000003576278687, "Z": 0.9999999403953552}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 0.09447580575942993, "Y": 11.951068878173828, "Z": -24.168603897094727}}, {"$$ClassName": "TCastleTransform", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "Lights", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "$Children": [{"$$ClassName": "TCastleSpotLight", "AttenuationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}, "Intensity": 4000.0, "Name": "SpotLight", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.1996897459030151, "X": -0.9998012185096741, "Y": 0.016462542116642, "Z": -0.011249393224716187}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 0.9999999403953552, "Y": 0.9999999403953552, "Z": 0.9999999403953552}, "Shadows": true, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -2.379899263381958, "Y": 16.15633201599121, "Z": 7.957741737365723}}, {"$$ClassName": "TCastleDirectionalLight", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}, "Name": "DirectionalLight1", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.4484344720840454, "X": -0.9537983536720276, "Y": -0.2915486991405487, "Z": -0.07258225232362747}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "Y": 15.424114227294922}}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneChessBoard1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Url": "castle-data:/chess/chess_board.gltf", "$Behaviors": [{"$$ClassName": "TCastleMeshCollider", "Mesh": "SceneChessBoard1", "Name": "MeshCollider1"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody2"}]}, {"$$ClassName": "TCastleTransform", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "Black<PERSON><PERSON><PERSON>", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "$Children": [{"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackRook1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.391302108764648, "Y": 0.7043132781982422, "Z": 8.610088348388672}, "Url": "castle-data:/chess/black_rook.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackRook2", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.391302108764648, "Y": 0.7043132781982422, "Z": -8.77271842956543}, "Url": "castle-data:/chess/black_rook.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackKing1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.3845219612121582, "Y": -1.0000001192092896}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.450037956237793, "Y": 0.7043113708496094, "Z": -1.3829574584960938}, "Url": "castle-data:/chess/black_king.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider1"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody1"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackQueen1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.386018753051758, "Y": 0.7043113708496094, "Z": 0.9454474449157715}, "Url": "castle-data:/chess/black_queen.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackKnight1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.710350751876831, "Y": -1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.364890098571777, "Y": 0.7043123245239258, "Z": 6.038848400115967}, "Url": "castle-data:/chess/black_knight.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackKnight2", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.710350751876831, "Y": -1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.635882377624512, "Y": 0.7043123245239258, "Z": -6.450775623321533}, "Url": "castle-data:/chess/black_knight.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackBishop1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.395358085632324, "Y": 0.704310417175293, "Z": -4.059769630432129}, "Url": "castle-data:/chess/black_bishop.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackBishop2", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.395358085632324, "Y": 0.704310417175293, "Z": 3.525547504425049}, "Url": "castle-data:/chess/black_bishop.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": -8.509511947631836}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn2", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": -6.261991024017334}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn3", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": -3.6601080894470215}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn4", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": -1.2352255582809448}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn5", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": 1.1866122484207153}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn6", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": 3.553868293762207}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn7", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": 6.163641452789307}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn8", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": 8.455798149108887}, "Url": "castle-data:/chess/black_pawn.gltf"}]}, {"$$ClassName": "TCastleTransform", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "White<PERSON><PERSON><PERSON>", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "$Children": [{"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn8", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": -8.502646446228027}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn7", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": -6.091457843780518}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn6", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": -3.649150848388672}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn5", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": -1.2063809633255005}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn4", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": 1.2783781290054321}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn3", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": 3.9117379188537598}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn2", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": 6.298854351043701}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": 8.629002571105957}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteKing1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.3180183172225952, "Y": -1.0000001192092896}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 0.9999999403953552, "Z": 0.9999999403953552}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.533949851989746, "Y": 0.7043113708496094, "Z": -1.3700945377349854}, "Url": "castle-data:/chess/white_king.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteBishop2", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "LineWidth": 5.0, "SilhouetteBias": 20.0, "SilhouetteScale": 20.0, "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent", "Green": 0.9215686321258545, "Red": 1.0}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.450355529785156, "Y": 0.7043132781982422, "Z": -3.711930990219116}, "Url": "castle-data:/chess/white_bishop.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteBishop1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.450355529785156, "Y": 0.7043132781982422, "Z": 4.003663539886475}, "Url": "castle-data:/chess/white_bishop.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteKnight2", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.4107271432876587, "Y": -1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 0.9999999403953552, "Z": 0.9999999403953552}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.304765701293945, "Y": 0.7043113708496094, "Z": -6.136622428894043}, "Url": "castle-data:/chess/white_knight.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteKnight1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.4311937093734741, "X": -0.00017263353220187128, "Y": -0.9999999403953552, "Z": 0.0001985873532248661}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent", "Y": 0.9999999403953552, "Z": 0.9999999403953552}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.304765701293945, "Y": 0.7043113708496094, "Z": 6.270512104034424}, "Url": "castle-data:/chess/white_knight.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteRook2", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.352005004882812, "Y": 0.7043113708496094, "Z": -8.531329154968262}, "Url": "castle-data:/chess/white_rook.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteRook1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.352005004882812, "Y": 0.7043113708496094, "Z": 8.835247039794922}, "Url": "castle-data:/chess/white_rook.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteQueen1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WholeSceneManifold": true, "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.270057678222656, "Y": 0.7043123245239258, "Z": 1.3464992046356201}, "Url": "castle-data:/chess/white_queen.gltf"}]}, {"$$ClassName": "TCastleTransformDesign", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Exists": false, "Name": "DesignForceGizmo", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Url": "castle-data:/force_gizmo.castle-transform"}]}, "Name": "MainViewport", "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent"}, "$NonVisualComponents": [{"$$ClassName": "TCastleBackground", "GroundBottomColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}, "GroundEquatorColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}, "Name": "Background1", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "SkyEquatorColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}, "SkyTopColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}], "InternalDesignCamera": {"$$ClassName": "TCastleCamera", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "GravityUpPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "", "Orthographic": {"$$ClassName": "TCastleOrthographic", "OriginPersistent": {"$$ClassName": "TCastleVector2Persistent"}}, "Perspective": {"$$ClassName": "TCastlePerspective"}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 3.1539440155029297, "X": 0.0010578942019492388, "Y": 0.9846253991127014, "Z": 0.1746761053800583}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 0.9999999403953552, "Y": 1.0000001192092896}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 1.100973129272461, "Y": 15.745317459106445, "Z": -29.48323631286621}}, "InternalDesignNavigations[dnFly]": {"$$ClassName": "TCastleWalkNavigationDesign", "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "MoveSpeed": 27.12272834777832, "Name": "", "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent"}}, "InternalDesignNavigations[dnExamine]": {"$$ClassName": "TCastleExamineNavigationDesign", "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "Exists": false, "Name": "", "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent"}}, "InternalDesignNavigations[dn2D]": {"$$ClassName": "TCastle2DNavigationDesign", "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "Exists": false, "Name": "", "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent"}}}, {"$$ClassName": "TCastleLabel", "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "ColorPersistent": {"$$ClassName": "TCastleColorPersistent", "Blue": 0.1764705926179886, "Green": 0.6117647290229797, "Red": 0.9882352948188782}, "FontSize": 20.0, "FrameColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "HorizontalAnchorParent": "hpRight", "HorizontalAnchorSelf": "hpRight", "Name": "LabelFps", "OutlineColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "Text": ["FPS: xxx"], "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent", "X": -20.0, "Y": -20.0}, "VerticalAnchorParent": "vpTop", "VerticalAnchorSelf": "vpTop"}]}