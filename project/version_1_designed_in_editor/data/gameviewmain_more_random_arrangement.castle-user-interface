{"$$ClassName": "TCastleUserInterface", "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "FullSize": true, "Name": "Group1", "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent"}, "$Children": [{"$$ClassName": "TCastleViewport", "Background": "Background1", "BackgroundColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "Camera": "Camera1", "FullSize": true, "Items": {"$$ClassName": "TCastleRootTransform", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "PhysicsProperties": {"$$ClassName": "TPhysicsProperties", "LayerCollisions": {"$$ClassName": "TCastleLayerCollisions"}, "LayerNames": {"$$ClassName": "TCastleLayerNames"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "$Children": [{"$$ClassName": "TCastleCamera", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "GravityUpPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "Camera1", "Orthographic": {"$$ClassName": "TCastleOrthographic", "OriginPersistent": {"$$ClassName": "TCastleVector2Persistent"}}, "Perspective": {"$$ClassName": "TCastlePerspective"}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.6778544187545776, "X": -0.32582294940948486, "Y": 0.888611376285553, "Z": 0.32281479239463806}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 0.9999999403953552, "Z": 0.9999999403953552}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 19.388948440551758, "Y": 16.648277282714844, "Z": -0.035093195736408234}}, {"$$ClassName": "TCastleTransform", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "Lights", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "Z": 1.0}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "$Children": [{"$$ClassName": "TCastleSpotLight", "AttenuationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}, "Intensity": 4000.0, "Name": "SpotLight", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.1996897459030151, "X": -0.9998012185096741, "Y": 0.016462542116642, "Z": -0.011249393224716187}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 0.9999999403953552, "Y": 0.9999999403953552, "Z": 0.9999999403953552}, "Shadows": true, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -2.379899263381958, "Y": 16.15633201599121, "Z": 7.957741737365723}}, {"$$ClassName": "TCastleDirectionalLight", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}, "Name": "DirectionalLight1", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.4484344720840454, "X": -0.9537983536720276, "Y": -0.2915486991405487, "Z": -0.07258225232362747}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "Y": 15.424114227294922}}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneChessBoard1", "PreciseCollisions": true, "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Url": "castle-data:/chess/chess_board.gltf", "$Behaviors": [{"$$ClassName": "TCastleMeshCollider", "Mesh": "SceneChessBoard1", "Name": "MeshCollider1"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody1"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteQueen1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.270057678222656, "Y": 0.7043123245239258, "Z": 1.3464992046356201}, "Url": "castle-data:/chess/white_queen.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteRook1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.352005004882812, "Y": 0.7043113708496094, "Z": 8.835247039794922}, "Url": "castle-data:/chess/white_rook.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteRook2", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.352005004882812, "Y": 0.7043113708496094, "Z": -8.531329154968262}, "Url": "castle-data:/chess/white_rook.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteKnight1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.4311937093734741, "Y": -0.9999998807907104}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.304765701293945, "Y": 0.7043113708496094, "Z": 6.270512104034424}, "Url": "castle-data:/chess/white_knight.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteKnight2", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.4107271432876587, "Y": -1.0000011920928955}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -0.024239685386419296, "Y": 2.00693678855896, "Z": -4.791903495788574}, "Url": "castle-data:/chess/white_knight.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider4"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody5"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteBishop1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.450355529785156, "Y": 0.7043132781982422, "Z": 4.003663539886475}, "Url": "castle-data:/chess/white_bishop.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteBishop2", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 8.450355529785156, "Y": 0.7043132781982422, "Z": -3.711930990219116}, "Url": "castle-data:/chess/white_bishop.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteKing1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.3180183172225952, "Y": -1.000001072883606}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 1.1326273679733276, "Y": 1.245832920074463, "Z": 0.5416901707649231}, "Url": "castle-data:/chess/white_king.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider2"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody3"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteKing2", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.3180183172225952, "Y": -1.000001072883606}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -0.826026976108551, "Y": 1.245832920074463, "Z": 4.8289570808410645}, "Url": "castle-data:/chess/white_king.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider8"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody9"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhiteKing3", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.3180183172225952, "Y": -1.000001072883606}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -3.6457653045654297, "Y": 1.245832920074463, "Z": -0.19603721797466278}, "Url": "castle-data:/chess/white_king.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider9"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody10"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": 8.629002571105957}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn2", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": 6.298854351043701}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn3", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": 3.9117379188537598}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn4", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": 1.2783781290054321}, "Url": "castle-data:/chess/white_pawn.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider10"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -100.0}, "Name": "RigidBody11"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn5", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -1.5085440874099731, "Y": 8.868751525878906, "Z": -1.2063809633255005}, "Url": "castle-data:/chess/white_pawn.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider1"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody2"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn9", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -1.5085440874099731, "Y": 8.868751525878906, "Z": 0.6898701190948486}, "Url": "castle-data:/chess/white_pawn.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider5"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody6"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn10", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -1.5085440874099731, "Y": 8.868751525878906, "Z": 2.9213123321533203}, "Url": "castle-data:/chess/white_pawn.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider6"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody7"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn11", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -1.5085440874099731, "Y": 8.868751525878906, "Z": 5.046355247497559}, "Url": "castle-data:/chess/white_pawn.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider7"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody8"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn6", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": -3.649150848388672}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn7", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 6.337164878845215, "Y": 0.6847987174987793, "Z": -6.091457843780518}, "Url": "castle-data:/chess/white_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneWhitePawn8", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -1.5650110244750977, "Y": 4.398053169250488, "Z": -0.32295429706573486}, "Url": "castle-data:/chess/white_pawn.gltf", "$Behaviors": [{"$$ClassName": "TCastleBoxCollider", "Name": "BoxCollider3"}, {"$$ClassName": "TCastleRigidBody", "AngularVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "LinearVelocityPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "RigidBody4"}]}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackRook1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.391302108764648, "Y": 0.7043132781982422, "Z": 8.610088348388672}, "Url": "castle-data:/chess/black_rook.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackRook2", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.391302108764648, "Y": 0.7043132781982422, "Z": -8.77271842956543}, "Url": "castle-data:/chess/black_rook.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackQueen1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.386018753051758, "Y": 0.7043113708496094, "Z": 0.9454474449157715}, "Url": "castle-data:/chess/black_queen.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackKing1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.3845219612121582, "Y": -0.9999988079071045}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.450037956237793, "Y": 0.7043113708496094, "Z": -1.3829574584960938}, "Url": "castle-data:/chess/black_king.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackKnight1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.710350751876831, "Y": -1.0000001192092896}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.364890098571777, "Y": 0.7043123245239258, "Z": 6.038848400115967}, "Url": "castle-data:/chess/black_knight.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackKnight2", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 1.710350751876831, "Y": -1.0000001192092896}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.635882377624512, "Y": 0.7043123245239258, "Z": -6.450775623321533}, "Url": "castle-data:/chess/black_knight.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackBishop1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.395358085632324, "Y": 0.704310417175293, "Z": -4.059769630432129}, "Url": "castle-data:/chess/black_bishop.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackBishop2", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -8.395358085632324, "Y": 0.704310417175293, "Z": 3.525547504425049}, "Url": "castle-data:/chess/black_bishop.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn1", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": -8.509511947631836}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn2", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": -6.261991024017334}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn3", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": -3.6601080894470215}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn4", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": -1.2352255582809448}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn5", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": 1.1866122484207153}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn6", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": 3.553868293762207}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn7", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": 6.163641452789307}, "Url": "castle-data:/chess/black_pawn.gltf"}, {"$$ClassName": "TCastleScene", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "ExposeTransforms": [], "Name": "SceneBlackPawn8", "RenderOptions": {"$$ClassName": "TCastleScene.TSceneRenderOptions", "WireframeColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent"}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": -6.162358283996582, "Y": 0.7043113708496094, "Z": 8.455798149108887}, "Url": "castle-data:/chess/black_pawn.gltf"}]}, "Name": "Viewport1", "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent"}, "$NonVisualComponents": [{"$$ClassName": "TCastleBackground", "GroundBottomColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}, "GroundEquatorColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}, "Name": "Background1", "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent"}, "SkyEquatorColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}, "SkyTopColorPersistent": {"$$ClassName": "TCastleColorRGBPersistent"}}], "InternalDesignCamera": {"$$ClassName": "TCastleCamera", "CenterPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "GravityUpPersistent": {"$$ClassName": "TCastleVector3Persistent"}, "Name": "", "Orthographic": {"$$ClassName": "TCastleOrthographic", "OriginPersistent": {"$$ClassName": "TCastleVector2Persistent"}}, "Perspective": {"$$ClassName": "TCastlePerspective"}, "RotationPersistent": {"$$ClassName": "TCastleVector4RotationPersistent", "W": 2.730163335800171, "X": -0.041639089584350586, "Y": 0.9792042374610901, "Z": 0.198557510972023}, "ScaleOrientationPersistent": {"$$ClassName": "TCastleVector4Persistent"}, "ScalePersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 0.9999999403953552, "Y": 1.0000001192092896}, "TranslationPersistent": {"$$ClassName": "TCastleVector3Persistent", "X": 10.894591331481934, "Y": 17.83517074584961, "Z": -30.442853927612305}}, "InternalDesignNavigations[dnFly]": {"$$ClassName": "TCastleWalkNavigationDesign", "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "MoveSpeed": 27.12272834777832, "Name": "", "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent"}}, "InternalDesignNavigations[dnExamine]": {"$$ClassName": "TCastleExamineNavigationDesign", "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "Exists": false, "Name": "", "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent"}}, "InternalDesignNavigations[dn2D]": {"$$ClassName": "TCastle2DNavigationDesign", "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "Exists": false, "Name": "", "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent"}}}, {"$$ClassName": "TCastleLabel", "Border": {"$$ClassName": "TBorder"}, "BorderColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "ColorPersistent": {"$$ClassName": "TCastleColorPersistent", "Blue": 0.1764705926179886, "Green": 0.6117647290229797, "Red": 0.9882352948188782}, "FontSize": 20.0, "FrameColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "HorizontalAnchorParent": "hpRight", "HorizontalAnchorSelf": "hpRight", "Name": "LabelFps", "OutlineColorPersistent": {"$$ClassName": "TCastleColorPersistent"}, "Text": ["FPS: xxx"], "TranslationPersistent": {"$$ClassName": "TCastleVector2Persistent", "X": -20.0, "Y": -20.0}, "VerticalAnchorParent": "vpTop", "VerticalAnchorSelf": "vpTop"}]}