{"asset": {"generator": "Khronos glTF Blender I/O v3.6.27", "version": "2.0"}, "scene": 0, "scenes": [{"name": "Scene", "nodes": [0, 1]}], "nodes": [{"mesh": 0, "name": "tiles", "scale": [11.564635276794434, 11.564635276794434, 11.564635276794434], "translation": [-0.3478984832763672, 0.7043114900588989, 1.1385812759399414]}, {"mesh": 1, "name": "simpler_frame", "translation": [-7.930201530456543, 0.6428251266479492, -9.548592567443848]}], "materials": [{"doubleSided": true, "name": "Material", "pbrMetallicRoughness": {"baseColorFactor": [0.800000011920929, 0.800000011920929, 0.800000011920929, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Material.001", "pbrMetallicRoughness": {"baseColorFactor": [0, 0, 0, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}, {"doubleSided": true, "name": "Material.002", "pbrMetallicRoughness": {"baseColorFactor": [0.1149197593331337, 0.07758805900812149, 0.04066706448793411, 1], "metallicFactor": 0, "roughnessFactor": 0.5}}], "meshes": [{"name": "Plane", "primitives": [{"attributes": {"POSITION": 0, "NORMAL": 1, "TEXCOORD_0": 2}, "indices": 3, "material": 0}, {"attributes": {"POSITION": 4, "NORMAL": 5, "TEXCOORD_0": 6}, "indices": 7, "material": 1}]}, {"name": "simpler_frame_mesh", "primitives": [{"attributes": {"POSITION": 8, "NORMAL": 9, "TEXCOORD_0": 10}, "indices": 11, "material": 2}]}], "accessors": [{"bufferView": 0, "componentType": 5126, "count": 232, "max": [0.8817291259765625, 0, 0.7478760480880737], "min": [-0.8200526237487793, -0.05763852223753929, -0.9539057016372681], "type": "VEC3"}, {"bufferView": 1, "componentType": 5126, "count": 232, "type": "VEC3"}, {"bufferView": 2, "componentType": 5126, "count": 232, "type": "VEC2"}, {"bufferView": 3, "componentType": 5123, "count": 768, "type": "SCALAR"}, {"bufferView": 4, "componentType": 5126, "count": 79, "max": [0.8817291259765625, 0, 0.7478760480880737], "min": [-0.8200526237487793, 0, -0.9539057016372681], "type": "VEC3"}, {"bufferView": 5, "componentType": 5126, "count": 79, "type": "VEC3"}, {"bufferView": 6, "componentType": 5126, "count": 79, "type": "VEC2"}, {"bufferView": 7, "componentType": 5123, "count": 192, "type": "SCALAR"}, {"bufferView": 8, "componentType": 5126, "count": 88, "max": [18.820886611938477, 0.5571749210357666, 20.387174606323242], "min": [-3.0791139602661133, -0.5428249835968018, -1.5128259658813477], "type": "VEC3"}, {"bufferView": 9, "componentType": 5126, "count": 88, "type": "VEC3"}, {"bufferView": 10, "componentType": 5126, "count": 88, "type": "VEC2"}, {"bufferView": 11, "componentType": 5123, "count": 198, "type": "SCALAR"}], "bufferViews": [{"buffer": 0, "byteLength": 2784, "byteOffset": 0, "target": 34962}, {"buffer": 0, "byteLength": 2784, "byteOffset": 2784, "target": 34962}, {"buffer": 0, "byteLength": 1856, "byteOffset": 5568, "target": 34962}, {"buffer": 0, "byteLength": 1536, "byteOffset": 7424, "target": 34963}, {"buffer": 0, "byteLength": 948, "byteOffset": 8960, "target": 34962}, {"buffer": 0, "byteLength": 948, "byteOffset": 9908, "target": 34962}, {"buffer": 0, "byteLength": 632, "byteOffset": 10856, "target": 34962}, {"buffer": 0, "byteLength": 384, "byteOffset": 11488, "target": 34963}, {"buffer": 0, "byteLength": 1056, "byteOffset": 11872, "target": 34962}, {"buffer": 0, "byteLength": 1056, "byteOffset": 12928, "target": 34962}, {"buffer": 0, "byteLength": 704, "byteOffset": 13984, "target": 34962}, {"buffer": 0, "byteLength": 396, "byteOffset": 14688, "target": 34963}], "buffers": [{"byteLength": 15084, "uri": "chess_board.bin"}]}